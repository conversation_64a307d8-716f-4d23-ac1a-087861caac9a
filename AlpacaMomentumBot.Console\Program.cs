using AlpacaMomentumBot.Services;
using DotNetEnv;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;

Env.Load();   // .env support

Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/alpaca-momentum-bot-.log",
                  rollingInterval: RollingInterval.Day,
                  retainedFileCountLimit: 30)
    .CreateLogger();

try
{
    Log.Information("Alpaca Momentum Bot — manual one-shot run");

    using IHost host = Host.CreateDefaultBuilder(args)
        .UseSerilog()
        .ConfigureServices(services =>
        {
            // infrastructure
            services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
            services.AddSingleton<IMarketSessionGuard, MarketSessionGuard>();
            services.AddSingleton<ITimeProvider, SystemTimeProvider>();
            services.AddSingleton<IUniverseProvider, FileUniverseProvider>();

            // strategy stack
            services.AddScoped<ISignalGenerator, SignalGenerator>();
            services.AddScoped<IRiskManager, RiskManager>();
            services.AddScoped<IPortfolioGate, PortfolioGate>();
            services.AddScoped<ITradeExecutor, TradeExecutor>();
            services.AddScoped<ITradingService, TradingService>();
        })
        .Build();

    using var scope   = host.Services.CreateScope();
    var guard         = scope.ServiceProvider.GetRequiredService<IMarketSessionGuard>();

    if (!await guard.CanTradeNowAsync())
    {
        Log.Information("Exiting — {Reason}", guard.Reason);
        return;
    }

    var trader = scope.ServiceProvider.GetRequiredService<ITradingService>();
    await trader.ExecuteCycleAsync();

    Log.Information("Trading cycle completed ✓");
}
catch (Exception ex)
{
    Log.Fatal(ex, "Fatal error — bot aborted");
}
finally
{
    Log.CloseAndFlush();
}

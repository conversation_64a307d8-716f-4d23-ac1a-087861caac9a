using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Services;

public sealed class AlpacaClientFactory : IAlpacaClientFactory
{
    private readonly ILogger<AlpacaClientFactory> _logger;

    public AlpacaClientFactory(ILogger<AlpacaClientFactory> logger)
    {
        _logger = logger;
    }

    public IAlpacaTradingClient CreateTradingClient()
    {
        var keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID") 
            ?? throw new InvalidOperationException("APCA_API_KEY_ID environment variable not set");
        
        var secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY") 
            ?? throw new InvalidOperationException("APCA_API_SECRET_KEY environment variable not set");
        
        var environment = Environment.GetEnvironmentVariable("APCA_API_ENV");
        var isPaper = string.Equals(environment, "paper", StringComparison.OrdinalIgnoreCase);
        
        _logger.LogInformation("Creating Alpaca trading client for {Environment} environment", 
            isPaper ? "paper" : "live");

        return Environments.Paper
            .GetAlpacaTradingClient(new SecretKey(keyId, secretKey));
    }

    public IAlpacaDataClient CreateDataClient()
    {
        var keyId = Environment.GetEnvironmentVariable("APCA_API_KEY_ID") 
            ?? throw new InvalidOperationException("APCA_API_KEY_ID environment variable not set");
        
        var secretKey = Environment.GetEnvironmentVariable("APCA_API_SECRET_KEY") 
            ?? throw new InvalidOperationException("APCA_API_SECRET_KEY environment variable not set");

        return Environments.Paper
            .GetAlpacaDataClient(new SecretKey(keyId, secretKey));
    }
}
